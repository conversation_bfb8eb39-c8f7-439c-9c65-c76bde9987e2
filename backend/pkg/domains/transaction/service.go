package transaction

import (
	"bufio"
	"errors"
	"fmt"
	"math"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/domains/account"
	"github.com/NocyTech/fin_notebook/pkg/domains/category"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
	"github.com/ledongthuc/pdf"
)

type Service interface {
	CreateTransaction(userID string, req *dtos.TransactionRequest) (*dtos.TransactionResponse, error)
	CreateBulkTransactions(userID string, req *dtos.BulkTransactionRequest) (*dtos.BulkTransactionResponse, error)
	GetTransactionByID(id string) (*dtos.TransactionResponse, error)
	UpdateTransaction(id string, req *dtos.TransactionUpdateRequest) (*dtos.TransactionResponse, error)
	DeleteTransaction(id string) error
	GetAllTransactions(userID string, filter *dtos.TransactionFilterRequest) (*dtos.PaginatedData, error)

	// Bank statement related methods
	parseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error)
}

type service struct {
	repository      Repository
	categoryService category.Service
	accountService  account.Service
}

func NewService(r Repository, cs category.Service, as account.Service) Service {
	return &service{
		repository:      r,
		categoryService: cs,
		accountService:  as,
	}
}

func (s *service) CreateTransaction(userID string, req *dtos.TransactionRequest) (*dtos.TransactionResponse, error) {
	// Parse UUIDs
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	categoryUUID, err := uuid.Parse(req.CategoryID)
	if err != nil {
		return nil, errors.New("invalid category ID")
	}

	accountUUID, err := uuid.Parse(req.AccountID)
	if err != nil {
		return nil, errors.New("invalid account ID")
	}

	// Create transaction entity
	transaction := &entities.Transaction{
		Title:           req.Title,
		Type:            req.Type,
		Amount:          req.Amount,
		Currency:        req.Currency,
		CategoryID:      categoryUUID,
		PaymentMethod:   req.PaymentMethod,
		AccountID:       accountUUID,
		Note:            req.Note,
		TransactionDate: req.TransactionDate,
		Location:        req.Location,
		UserID:          userUUID,
	}

	// Save transaction
	if err := s.repository.Create(transaction); err != nil {
		return nil, err
	}

	// Update account balance
	switch req.Type {
	case "expense":
		if err := s.accountService.UpdateBalance(req.AccountID, -req.Amount); err != nil {
			return nil, err
		}
	case "income":
		if err := s.accountService.UpdateBalance(req.AccountID, req.Amount); err != nil {
			return nil, err
		}
	}

	// Get transaction with relations
	savedTransaction, err := s.repository.FindByID(transaction.ID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toTransactionResponse(savedTransaction), nil
}

func (s *service) GetTransactionByID(id string) (*dtos.TransactionResponse, error) {
	// Parse UUID
	transactionUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid transaction ID")
	}

	// Get transaction
	transaction, err := s.repository.FindByID(transactionUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toTransactionResponse(transaction), nil
}

func (s *service) UpdateTransaction(id string, req *dtos.TransactionUpdateRequest) (*dtos.TransactionResponse, error) {
	// Parse UUID
	transactionUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid transaction ID")
	}

	// Get transaction
	transaction, err := s.repository.FindByID(transactionUUID)
	if err != nil {
		return nil, err
	}

	// Store old values for account balance update
	oldAmount := transaction.Amount
	oldAccountID := transaction.AccountID.String()

	// Update transaction fields
	if req.Title != "" {
		transaction.Title = req.Title
	}
	if req.Amount > 0 {
		transaction.Amount = req.Amount
	}
	if req.Currency != "" {
		transaction.Currency = req.Currency
	}
	if req.CategoryID != "" {
		categoryUUID, err := uuid.Parse(req.CategoryID)
		if err != nil {
			return nil, errors.New("invalid category ID")
		}
		transaction.CategoryID = categoryUUID
	}
	if req.PaymentMethod != "" {
		transaction.PaymentMethod = req.PaymentMethod
	}
	if req.AccountID != "" {
		accountUUID, err := uuid.Parse(req.AccountID)
		if err != nil {
			return nil, errors.New("invalid account ID")
		}
		transaction.AccountID = accountUUID
	}
	if req.Note != "" {
		transaction.Note = req.Note
	}
	if !req.TransactionDate.IsZero() {
		transaction.TransactionDate = req.TransactionDate
	}
	if req.Location != "" {
		transaction.Location = req.Location
	}

	// Save transaction
	if err := s.repository.Update(transaction); err != nil {
		return nil, err
	}

	// Update account balance
	switch transaction.Type {
	case "expense":
		// Revert old amount
		if err := s.accountService.UpdateBalance(oldAccountID, oldAmount); err != nil {
			return nil, err
		}
		// Apply new amount
		if err := s.accountService.UpdateBalance(transaction.AccountID.String(), -transaction.Amount); err != nil {
			return nil, err
		}
	case "income":
		// Revert old amount
		if err := s.accountService.UpdateBalance(oldAccountID, -oldAmount); err != nil {
			return nil, err
		}
		// Apply new amount
		if err := s.accountService.UpdateBalance(transaction.AccountID.String(), transaction.Amount); err != nil {
			return nil, err
		}
	}

	// Get updated transaction
	updatedTransaction, err := s.repository.FindByID(transaction.ID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toTransactionResponse(updatedTransaction), nil
}

func (s *service) DeleteTransaction(id string) error {
	// Parse UUID
	transactionUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid transaction ID")
	}

	// Get transaction
	transaction, err := s.repository.FindByID(transactionUUID)
	if err != nil {
		return err
	}

	// Update account balance
	switch transaction.Type {
	case "expense":
		if err := s.accountService.UpdateBalance(transaction.AccountID.String(), transaction.Amount); err != nil {
			return err
		}
	case "income":
		if err := s.accountService.UpdateBalance(transaction.AccountID.String(), -transaction.Amount); err != nil {
			return err
		}
	}

	// Delete transaction
	return s.repository.Delete(transactionUUID)
}

func (s *service) GetAllTransactions(userID string, filter *dtos.TransactionFilterRequest) (*dtos.PaginatedData, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Get total count first
	totalCount, err := s.repository.GetTotalCount(userUUID, filter)
	if err != nil {
		return nil, err
	}

	// Get summary
	summary, err := s.repository.GetSummary(userUUID, filter)
	if err != nil {
		return nil, err
	}

	// Get transactions
	transactions, err := s.repository.FindAll(userUUID, filter)
	if err != nil {
		return nil, err
	}

	// Convert to response
	var response []dtos.TransactionResponse
	for _, transaction := range transactions {
		response = append(response, *s.toTransactionResponse(&transaction))
	}

	// Calculate pagination info
	page := int64(1)
	if filter.Page > 0 {
		page = int64(filter.Page)
	}

	limit := int64(10)
	if filter.Limit > 0 {
		limit = int64(filter.Limit)
	}

	totalPages := int((totalCount + limit - 1) / limit)

	return &dtos.PaginatedData{
		Page:       page,
		PerPage:    limit,
		Total:      totalCount,
		TotalPages: totalPages,
		Rows:       response,
		Summary:    summary,
	}, nil
}

func (s *service) toTransactionResponse(transaction *entities.Transaction) *dtos.TransactionResponse {
	// Get category separately
	category, err := s.categoryService.GetCategoryByID(transaction.CategoryID.String())
	if err != nil {
		// If category not found, use empty category
		category = &dtos.CategoryResponse{
			ID:   transaction.CategoryID.String(),
			Name: "Unknown Category",
			Type: "expense",
			Icon: "help",
		}
	}

	// Get account separately
	account, err := s.accountService.GetAccountByID(transaction.AccountID.String())
	if err != nil {
		// If account not found, use empty account
		account = &dtos.AccountResponse{
			ID:       transaction.AccountID.String(),
			Name:     "Unknown Account",
			Type:     "cash",
			Balance:  0,
			Currency: "TRY",
		}
	}

	return &dtos.TransactionResponse{
		ID:         transaction.ID.String(),
		Title:      transaction.Title,
		Type:       transaction.Type,
		Amount:     transaction.Amount,
		Currency:   transaction.Currency,
		CategoryID: transaction.CategoryID.String(),
		Category: dtos.CategoryDTO{
			ID:   category.ID,
			Name: category.Name,
			Type: category.Type,
			Icon: category.Icon,
		},
		PaymentMethod: transaction.PaymentMethod,
		AccountID:     transaction.AccountID.String(),
		Account: dtos.AccountDTO{
			ID:       account.ID,
			Name:     account.Name,
			Type:     account.Type,
			Balance:  account.Balance,
			Currency: account.Currency,
		},
		Note:            transaction.Note,
		TransactionDate: transaction.TransactionDate,
		Location:        transaction.Location,
		CreatedAt:       transaction.CreatedAt,
		UpdatedAt:       transaction.UpdatedAt,
	}
}

// parseVakifbankStatement parses a Vakifbank statement PDF and extracts transactions
func (s *service) parseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	// Read the PDF file
	pdfReader, err := pdf.NewReader(file, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to read PDF: %w", err)
	}

	// Extract text from PDF
	var extractedText string
	for pageIndex := 1; pageIndex <= pdfReader.NumPage(); pageIndex++ {
		page := pdfReader.Page(pageIndex)
		if page.V.IsNull() {
			continue
		}

		text, err := page.GetPlainText(nil)
		if err != nil {
			continue
		}
		extractedText += text
	}

	// Parse the extracted text to find transactions
	return s.parseVakifbankStatementText(extractedText)
}

// parseVakifbankStatementText parses the extracted text from a Vakifbank statement
func (s *service) parseVakifbankStatementText(text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	// Split the text into lines
	scanner := bufio.NewScanner(strings.NewReader(text))
	var lines []string
	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) != "" {
			lines = append(lines, line)
		}
	}

	// Find the header line
	headerIndex := -1
	for i, line := range lines {
		if strings.Contains(line, "TARİH") && strings.Contains(line, "SAAT") &&
			strings.Contains(line, "İŞLEM NO") && strings.Contains(line, "MİKTAR") &&
			strings.Contains(line, "BAKİYE") && strings.Contains(line, "İŞLEM ADI") {
			headerIndex = i
			fmt.Printf("Found header line at index: %d Content: %s\n", i, line)
			break
		}
	}

	if headerIndex == -1 {
		return nil, errors.New("could not find transaction header in statement")
	}

	// Process transaction lines
	// Regular expression to match date, amount, and description
	dateRegex := regexp.MustCompile(`(\d{2}\.\d{2}\.\d{4})\s+(\d{2}:\d{2})\s+(\d+)\s+([+-]?[\d,.]+)\s+([+-]?[\d,.]+)\s+(.+)`)

	for i := headerIndex + 1; i < len(lines); i++ {
		line := lines[i]

		// Skip empty lines
		if strings.TrimSpace(line) == "" {
			continue
		}

		// Try to match the line with our regex
		matches := dateRegex.FindStringSubmatch(line)
		if len(matches) >= 7 {
			date := matches[1]
			amount := matches[4]
			description := matches[6]

			// Clean and parse the amount
			amount = strings.ReplaceAll(amount, ".", "")
			amount = strings.ReplaceAll(amount, ",", ".")
			amountFloat, err := strconv.ParseFloat(amount, 64)
			if err != nil {
				continue
			}

			// Determine transaction type
			transactionType := "expense"
			if amountFloat > 0 {
				transactionType = "income"
			}

			// Create entry
			entry := dtos.BankStatementEntry{
				Date:        date,
				Description: description,
				Amount:      math.Abs(amountFloat),
				Type:        transactionType,
			}

			entries = append(entries, entry)
		}
	}

	return entries, nil
}

// ImportBankStatementEntries imports bank statement entries as transactions
func (s *service) ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error) {
	var responses []dtos.TransactionResponse

	for _, entry := range entries {
		// Convert date format if needed
		transactionDate, err := time.Parse("02.01.2006", entry.Date)
		if err != nil {
			// Try alternative format
			transactionDate, err = time.Parse("2006-01-02", entry.Date)
			if err != nil {
				return nil, fmt.Errorf("invalid date format: %s", entry.Date)
			}
		}

		// Create transaction request
		transactionReq := &dtos.TransactionRequest{
			Title:           entry.Description,
			Type:            entry.Type,
			Amount:          entry.Amount,
			Currency:        "TRY", // Default to Turkish Lira
			CategoryID:      entry.CategoryID,
			PaymentMethod:   "bank_transfer", // Default payment method
			AccountID:       entry.AccountID,
			TransactionDate: transactionDate,
		}

		// Create transaction
		response, err := s.CreateTransaction(userID, transactionReq)
		if err != nil {
			return nil, err
		}

		responses = append(responses, *response)
	}

	return responses, nil
}

func (s *service) CreateBulkTransactions(userID string, req *dtos.BulkTransactionRequest) (*dtos.BulkTransactionResponse, error) {
	response := &dtos.BulkTransactionResponse{
		SuccessCount: 0,
		FailureCount: 0,
		Successful:   []dtos.TransactionResponse{},
		Failed:       []dtos.BulkTransactionError{},
	}

	// Process each transaction
	for i, transactionReq := range req.Transactions {
		// Create transaction
		transactionResp, err := s.CreateTransaction(userID, &transactionReq)
		if err != nil {
			// Add to failed list
			response.Failed = append(response.Failed, dtos.BulkTransactionError{
				Index:   i,
				Error:   err.Error(),
				Request: transactionReq,
			})
			response.FailureCount++
		} else {
			// Add to successful list
			response.Successful = append(response.Successful, *transactionResp)
			response.SuccessCount++
		}
	}

	return response, nil
}
